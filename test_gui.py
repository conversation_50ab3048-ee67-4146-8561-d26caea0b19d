#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI界面的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt6.QtWidgets import QApplication
    from gui_optimized import BaiduPanGUI
    
    def main():
        print("启动GUI测试...")
        app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = BaiduPanGUI()
        gui.show()
        
        print("GUI界面已启动，检查界面显示效果...")
        print("- 检查灰黑色主题是否正确应用")
        print("- 检查日志区域背景色是否为深色")
        print("- 检查文字颜色是否清晰可读")
        
        # 运行应用
        sys.exit(app.exec())
    
    if __name__ == '__main__':
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyQt6: pip install PyQt6")
except Exception as e:
    print(f"启动失败: {e}")

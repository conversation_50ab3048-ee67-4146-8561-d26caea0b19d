import time
import os
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class BaiduPanSaverFast:
    def __init__(self, log_callback=None):
        self.driver = None
        self.log_callback = log_callback
        self.cookies_file = "baidu_cookies.json"
        self.is_logged_in = False

    def _log(self, message, level='info'):
        """统一的日志记录方法，支持回调到GUI。"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [{level.upper()}] {message}"
        
        if self.log_callback:
            self.log_callback(formatted_message)
        else:
            if level == 'info':
                logging.info(message)
            elif level == 'warning':
                logging.warning(message)
            else:
                logging.error(message)

    def _initialize_driver(self):
        """初始化 Chrome WebDriver。"""
        self._log("正在初始化 Chrome WebDriver...")
        options = webdriver.ChromeOptions()
        
        # 添加稳定性选项
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-images')  # 禁用图片加载
        options.page_load_strategy = 'eager'  # 不等待所有资源加载完成
        
        try:
            self.driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options)
            self.driver.set_page_load_timeout(10)  # 页面加载超时10秒
            self.driver.implicitly_wait(2)  # 隐式等待2秒
            
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self._log("WebDriver 初始化完成")
        except Exception as e:
            self._log(f"WebDriver 初始化失败: {e}", level='error')
            raise

    def load_cookies(self):
        """从文件加载cookies。"""
        try:
            if os.path.exists(self.cookies_file):
                with open(self.cookies_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                
                self.driver.get("https://pan.baidu.com")
                time.sleep(1)
                
                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except:
                        pass
                
                self.driver.refresh()
                time.sleep(2)
                
                if self.check_login_status():
                    self._log("使用保存的cookies登录成功")
                    self.is_logged_in = True
                    return True
                else:
                    self._log("cookies已过期，需要重新登录", level='warning')
                    return False
            else:
                self._log("未找到保存的cookies文件")
                return False
        except Exception as e:
            self._log(f"加载cookies失败: {e}", level='error')
            return False

    def check_login_status(self):
        """检查是否已登录。"""
        try:
            login_indicators = [
                "//div[contains(@class, 'user-info')]",
                "//div[contains(@class, 'avatar')]",
                "//span[contains(@class, 'username')]",
                "//*[contains(@class, 'user-name')]"
            ]
            
            for indicator in login_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element and element.is_displayed():
                        return True
                except:
                    continue
            return False
        except:
            return False

    def save_cookies(self):
        """保存当前的cookies到文件。"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            self._log(f"Cookies已保存到 {self.cookies_file}")
        except Exception as e:
            self._log(f"保存cookies失败: {e}", level='error')

    def open_login_page(self):
        """打开百度网盘页面让用户登录。"""
        if not self.driver:
            self._initialize_driver()
        
        self._log("尝试使用保存的cookies自动登录...")
        if self.load_cookies():
            self._log("自动登录成功，无需手动登录")
            return True
        
        self._log("自动登录失败，打开登录页面...")
        self.driver.get("https://pan.baidu.com")
        self._log("请在浏览器窗口中登录您的百度账号")
        return False

    def wait_for_login(self, timeout=300):
        """等待用户完成登录，并保存cookies。"""
        self._log(f"等待用户登录中... (超时时间: {timeout}秒)")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.check_login_status():
                self._log("检测到登录成功")
                self.save_cookies()
                self.is_logged_in = True
                return True
            time.sleep(2)
        
        self._log("等待登录超时", level='error')
        return False

    def navigate_and_create_folder(self, target_folder_name):
        """导航并创建目标文件夹 - 简化版本"""
        try:
            self._log(f"设置保存路径: /hh客户/{target_folder_name}")

            # 分步骤执行，更可靠
            # 1. 先查找并点击hh客户文件夹
            js_find_hh = """
            var found = false;
            var elements = document.querySelectorAll('span, div, li');
            for (var i = 0; i < elements.length; i++) {
                var elem = elements[i];
                var text = (elem.textContent || elem.innerText || '').trim();
                if (text === 'hh客户') {
                    elem.click();
                    found = true;
                    break;
                }
            }
            return found;
            """

            hh_found = self.driver.execute_script(js_find_hh)
            if hh_found:
                self._log("已选择hh客户文件夹")
                time.sleep(0.5)

                # 2. 查找目标文件夹是否存在
                js_find_target = f"""
                var found = false;
                var elements = document.querySelectorAll('span, div, li');
                for (var i = 0; i < elements.length; i++) {{
                    var elem = elements[i];
                    var text = (elem.textContent || elem.innerText || '').trim();
                    if (text === '{target_folder_name}') {{
                        elem.click();
                        found = true;
                        break;
                    }}
                }}
                return found;
                """

                target_found = self.driver.execute_script(js_find_target)
                if target_found:
                    self._log(f"已选择目标文件夹: {target_folder_name}")
                else:
                    self._log(f"目标文件夹不存在，尝试创建: {target_folder_name}")
                    # 3. 创建新文件夹
                    self.create_new_folder(target_folder_name)
            else:
                self._log("未找到hh客户文件夹，将保存到根目录", level='warning')

            return True

        except Exception as e:
            self._log(f"设置保存路径失败: {e}", level='warning')
            return True  # 即使失败也继续保存

    def create_new_folder(self, folder_name):
        """创建新文件夹"""
        try:
            # 查找新建文件夹按钮
            js_create = f"""
            // 查找新建文件夹按钮
            var createBtn = null;
            var buttons = document.querySelectorAll('button, a');
            for (var i = 0; i < buttons.length; i++) {{
                var btn = buttons[i];
                var text = (btn.textContent || btn.innerText || '').trim();
                var title = btn.title || '';
                if (text.includes('新建文件夹') || title.includes('新建文件夹')) {{
                    createBtn = btn;
                    break;
                }}
            }}

            if (createBtn) {{
                createBtn.click();
                return true;
            }}
            return false;
            """

            create_clicked = self.driver.execute_script(js_create)
            if create_clicked:
                self._log("已点击新建文件夹")
                time.sleep(0.5)

                # 输入文件夹名称
                js_input = f"""
                var inputs = document.querySelectorAll('input[type="text"]');
                for (var i = 0; i < inputs.length; i++) {{
                    var input = inputs[i];
                    if (input.offsetParent !== null) {{ // 确保输入框可见
                        input.value = '{folder_name}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));

                        // 按回车确认
                        var enterEvent = new KeyboardEvent('keydown', {{
                            key: 'Enter',
                            keyCode: 13,
                            bubbles: true
                        }});
                        input.dispatchEvent(enterEvent);
                        return true;
                    }}
                }}
                return false;
                """

                input_success = self.driver.execute_script(js_input)
                if input_success:
                    self._log(f"已创建文件夹: {folder_name}")
                    time.sleep(1)
                else:
                    self._log("输入文件夹名称失败", level='warning')
            else:
                self._log("未找到新建文件夹按钮", level='warning')

        except Exception as e:
            self._log(f"创建文件夹时出错: {e}", level='warning')

    def navigate_to_folder(self, target_folder_name):
        """导航到已存在的目标文件夹"""
        try:
            # 简化版本：直接查找并点击目标路径
            js_navigate = f"""
            // 先点击hh客户
            var hhFound = false;
            var elements = document.querySelectorAll('span, div, li');
            for (var i = 0; i < elements.length; i++) {{
                var elem = elements[i];
                var text = (elem.textContent || elem.innerText || '').trim();
                if (text === 'hh客户') {{
                    elem.click();
                    hhFound = true;
                    break;
                }}
            }}

            if (hhFound) {{
                // 再查找目标文件夹
                setTimeout(function() {{
                    var elements2 = document.querySelectorAll('span, div, li');
                    for (var j = 0; j < elements2.length; j++) {{
                        var elem2 = elements2[j];
                        var text2 = (elem2.textContent || elem2.innerText || '').trim();
                        if (text2 === '{target_folder_name}') {{
                            elem2.click();
                            break;
                        }}
                    }}
                }}, 300);
            }}

            return hhFound;
            """

            result = self.driver.execute_script(js_navigate)
            time.sleep(0.5)
            return result

        except Exception as e:
            self._log(f"导航失败: {e}", level='warning')
            return False

    def save_links_fast(self, links_file):
        """快速保存链接 - 简化版本"""
        if not self.driver or not self.is_logged_in:
            self._log("请先完成登录", level='error')
            return False

        # 计算目标文件夹名
        base_name = os.path.basename(links_file)
        file_name_without_ext = os.path.splitext(base_name)[0]
        if len(file_name_without_ext) < 4:
            target_folder_name = file_name_without_ext
        else:
            target_folder_name = file_name_without_ext[-4:]

        self._log(f"目标文件夹: /hh客户/{target_folder_name}")

        # 读取链接
        links_to_save = []
        try:
            with open(links_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    parts = line.split()
                    if not parts:
                        continue
                    url = parts[0]
                    code = parts[1] if len(parts) > 1 else None

                    # 处理URL中的提取码
                    if '?pwd=' in url and not code:
                        parts = url.split('?pwd=')
                        if len(parts) == 2:
                            url = parts[0]
                            code = parts[1]

                    links_to_save.append({'url': url, 'code': code, 'line': line_num})
        except Exception as e:
            self._log(f"读取文件失败: {e}", level='error')
            return False

        if not links_to_save:
            self._log("链接文件为空", level='warning')
            return False

        self._log(f"共找到 {len(links_to_save)} 个链接待处理")

        success_count = 0
        failed_count = 0
        folder_created = False  # 标记是否已创建文件夹

        for i, link_info in enumerate(links_to_save, 1):
            url = link_info['url']
            code = link_info['code']
            line_num = link_info['line']
            
            self._log(f"[{i}/{len(links_to_save)}] 处理第{line_num}行链接")
            
            try:
                # 访问链接
                self.driver.get(url)
                time.sleep(1)

                # 输入提取码
                if code:
                    try:
                        code_input = WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((By.ID, "accessCode"))
                        )
                        code_input.clear()
                        code_input.send_keys(code)
                        self.driver.find_element(By.ID, "submitBtn").click()
                        time.sleep(1)
                    except:
                        pass

                # 等待页面加载
                time.sleep(2)
                
                # 直接使用JavaScript点击保存按钮
                js_click_save = """
                var buttons = document.querySelectorAll('button, a');
                for (var i = 0; i < buttons.length; i++) {
                    var btn = buttons[i];
                    var text = (btn.textContent || btn.innerText || '').trim();
                    var title = btn.title || '';
                    if (text.includes('存到我的网盘') || text.includes('保存') || text.includes('转存') || 
                        title.includes('存到我的网盘')) {
                        btn.click();
                        return true;
                    }
                }
                return false;
                """
                
                save_clicked = self.driver.execute_script(js_click_save)
                if save_clicked:
                    self._log("已点击保存按钮")
                    time.sleep(1)

                    # 导航到目标文件夹
                    if not folder_created:
                        self.navigate_and_create_folder(target_folder_name)
                        folder_created = True
                    else:
                        self.navigate_to_folder(target_folder_name)

                    # 点击确定
                    js_click_confirm = """
                    var buttons = document.querySelectorAll('button, a');
                    for (var i = 0; i < buttons.length; i++) {
                        var btn = buttons[i];
                        var text = (btn.textContent || btn.innerText || '').trim();
                        if (text === '确定' || text === '保存' || text === '存储') {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                    """

                    confirm_clicked = self.driver.execute_script(js_click_confirm)
                    if confirm_clicked:
                        self._log("已点击确定按钮")
                        success_count += 1
                    else:
                        self._log("未找到确定按钮，但保存请求已发送")
                        success_count += 1
                else:
                    self._log("未找到保存按钮", level='warning')
                    failed_count += 1

            except Exception as e:
                self._log(f"处理链接失败: {e}", level='error')
                failed_count += 1
        
        self._log(f"任务完成！成功: {success_count}, 失败: {failed_count}, 总计: {len(links_to_save)}")
        return success_count > 0

    def close(self):
        """关闭 WebDriver。"""
        if self.driver:
            self.driver.quit()
            self._log("WebDriver 已关闭")

def main():
    """命令行版本的执行函数。"""
    links_file = "links.txt"
    saver = BaiduPanSaverFast()
    
    if not saver.open_login_page():
        if not saver.wait_for_login():
            print("登录失败，程序退出。")
            saver.close()
            return
    
    saver.save_links_fast(links_file)
    saver.close()
    logging.info("所有链接处理完毕。")

if __name__ == "__main__":
    main()

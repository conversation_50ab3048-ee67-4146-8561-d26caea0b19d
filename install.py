#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度网盘批量转存工具安装脚本
"""

import sys
import subprocess
import os

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_requirements():
    """安装依赖包"""
    print("🔧 开始安装依赖包...")
    
    # 检查pip
    success, _, _ = run_command("pip --version")
    if not success:
        success, _, _ = run_command("pip3 --version")
        if not success:
            print("❌ 未找到pip，请先安装Python和pip")
            return False
        pip_cmd = "pip3"
    else:
        pip_cmd = "pip"
    
    # 升级pip
    print("📦 升级pip...")
    run_command(f"{pip_cmd} install --upgrade pip")
    
    # 安装依赖
    requirements = [
        "selenium>=4.0.0",
        "webdriver-manager>=3.8.0", 
        "PyQt6>=6.4.0"
    ]
    
    for req in requirements:
        print(f"📦 安装 {req}...")
        success, stdout, stderr = run_command(f"{pip_cmd} install {req}")
        if success:
            print(f"✅ {req} 安装成功")
        else:
            print(f"❌ {req} 安装失败: {stderr}")
            return False
    
    return True

def check_chrome():
    """检查Chrome浏览器"""
    print("🌐 检查Chrome浏览器...")
    
    # 常见的Chrome路径
    chrome_paths = [
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",  # macOS
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",     # Windows
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",  # Windows 32-bit
        "/usr/bin/google-chrome",  # Linux
        "/usr/bin/chromium-browser"  # Linux Chromium
    ]
    
    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_found = True
            print(f"✅ 找到Chrome浏览器: {path}")
            break
    
    if not chrome_found:
        print("⚠️  未找到Chrome浏览器，请确保已安装Chrome")
        print("   下载地址: https://www.google.com/chrome/")
        return False
    
    return True

def create_desktop_shortcut():
    """创建桌面快捷方式（可选）"""
    try:
        if sys.platform == "darwin":  # macOS
            print("💡 提示: 可以将 run.py 拖到Dock或桌面创建快捷方式")
        elif sys.platform == "win32":  # Windows
            print("💡 提示: 可以右键 run.py 创建桌面快捷方式")
        else:  # Linux
            print("💡 提示: 可以创建 run.py 的桌面快捷方式")
    except:
        pass

def main():
    """主函数"""
    print("🌟 百度网盘批量转存工具安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"   当前版本: {sys.version}")
        return
    
    print(f"✅ Python版本检查通过: {sys.version}")
    
    # 安装依赖
    if not install_requirements():
        print("❌ 依赖安装失败")
        return
    
    # 检查Chrome
    if not check_chrome():
        print("⚠️  Chrome检查失败，但程序仍可能正常运行")
    
    # 创建快捷方式提示
    create_desktop_shortcut()
    
    print("\n🎉 安装完成！")
    print("📋 使用方法:")
    print("   1. 运行: python3 run.py")
    print("   2. 或者直接运行: python3 gui_optimized.py")
    print("\n📖 详细说明请查看 README_优化版.md")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 安装已取消")
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
    
    input("\n按回车键退出...")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度网盘批量转存工具启动脚本
支持自动检测环境并启动合适的版本
"""

import sys
import os
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['selenium', 'webdriver_manager']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    # 检查PyQt6
    try:
        import PyQt6
        gui_available = True
    except ImportError:
        gui_available = False
        missing_packages.append('PyQt6')
    
    return missing_packages, gui_available

def install_dependencies(packages):
    """安装缺失的依赖"""
    print("🔧 正在安装缺失的依赖包...")
    for package in packages:
        print(f"📦 安装 {package}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    return True

def main():
    """主函数"""
    print("🌟 百度网盘批量转存工具 v2.0")
    print("=" * 50)
    
    # 检查依赖
    missing_packages, gui_available = check_dependencies()
    
    if missing_packages:
        print("⚠️  检测到缺失的依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        
        choice = input("\n是否自动安装缺失的依赖？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            if not install_dependencies(missing_packages):
                print("❌ 依赖安装失败，请手动安装后重试")
                return
            # 重新检查
            missing_packages, gui_available = check_dependencies()
        else:
            print("❌ 请手动安装依赖后重试")
            return
    
    if missing_packages:
        print("❌ 仍有依赖未安装，程序无法运行")
        return
    
    print("✅ 所有依赖检查完成")
    
    # 选择运行模式
    if gui_available:
        print("\n🎮 请选择运行模式:")
        print("1. 图形界面版本 (推荐)")
        print("2. 命令行版本")
        
        while True:
            choice = input("\n请输入选择 (1/2): ").strip()
            if choice == '1':
                print("🚀 启动图形界面版本...")
                try:
                    from gui_optimized import main as gui_main
                    gui_main()
                except Exception as e:
                    print(f"❌ 启动图形界面失败: {e}")
                    print("🔄 尝试启动命令行版本...")
                    from main_optimized import main as cli_main
                    cli_main()
                break
            elif choice == '2':
                print("🚀 启动命令行版本...")
                from main_optimized import main as cli_main
                cli_main()
                break
            else:
                print("❌ 无效选择，请输入 1 或 2")
    else:
        print("🚀 启动命令行版本...")
        from main_optimized import main as cli_main
        cli_main()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        input("按回车键退出...")

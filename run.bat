@echo off
chcp 65001 >nul
title 百度网盘批量转存工具 v2.0

echo 🌟 百度网盘批量转存工具 v2.0
echo ================================

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

REM 尝试运行程序
echo 🚀 启动程序...
python run.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行失败，尝试安装依赖...
    python install.py
    echo.
    echo 🔄 重新启动程序...
    python run.py
)

pause

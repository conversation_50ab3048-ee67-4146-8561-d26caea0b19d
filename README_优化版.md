# 🌟 百度网盘批量转存工具 v2.0

## ✨ 新功能特性

### 🍪 自动登录功能
- **自动保存登录状态**：首次登录后，程序会自动保存cookies
- **免重复登录**：下次使用时自动加载保存的登录状态
- **智能检测**：自动检测登录状态是否有效

### 🎨 界面优化
- **现代化UI设计**：美观的图形界面，支持深色主题
- **实时进度显示**：显示当前处理进度和状态
- **详细日志输出**：带时间戳的彩色日志，支持保存和清空
- **智能错误处理**：更好的错误提示和恢复机制

### 🔧 功能改进
- **更强的兼容性**：支持多种页面元素选择器，提高成功率
- **智能重试机制**：自动重试失败的操作
- **批量处理优化**：更高效的文件夹创建和文件保存逻辑
- **详细统计信息**：显示成功/失败数量和处理时间

## 📋 使用说明

### 1. 环境准备
```bash
# 安装依赖
pip install selenium webdriver-manager PyQt6

# 确保Chrome浏览器已安装
```

### 2. 准备链接文件
创建一个文本文件（如 `links.txt`），格式如下：
```
# 注释行以#开头
https://pan.baidu.com/s/1xxxxxxxxx xxxx
https://pan.baidu.com/s/1yyyyyyyyy yyyy
https://pan.baidu.com/s/1zzzzzzzzz
```

### 3. 运行程序

#### 图形界面版本（推荐）
```bash
python gui_optimized.py
```

#### 命令行版本
```bash
python main_optimized.py
```

### 4. 操作步骤

1. **选择文件**：点击"浏览"按钮选择包含链接的文本文件
2. **登录账号**：点击"登录百度账号"按钮
   - 首次使用：会打开浏览器，请手动登录
   - 再次使用：会自动使用保存的登录状态
3. **开始转存**：登录成功后，点击"开始转存"按钮
4. **查看进度**：在界面中实时查看处理进度和日志

## 📁 文件说明

- `main_optimized.py` - 优化后的核心逻辑
- `gui_optimized.py` - 优化后的图形界面
- `main.py` - 原始版本（保留）
- `gui.py` - 原始GUI版本（保留）
- `links_example.txt` - 链接文件示例
- `baidu_cookies.json` - 自动生成的登录状态文件

## 🔧 配置说明

### 目标文件夹命名规则
- 程序会根据链接文件名的**后4位字符**作为目标文件夹名
- 例如：`links_0123.txt` → 目标文件夹为 `0123`
- 所有文件将保存到 `/hh客户/[文件夹名]/` 目录

### 自动保存cookies
- 登录成功后会自动保存到 `baidu_cookies.json`
- 下次启动时会自动加载，无需重复登录
- 如果cookies过期，程序会自动提示重新登录

## ⚠️ 注意事项

1. **网络环境**：确保网络连接稳定
2. **浏览器版本**：建议使用最新版Chrome浏览器
3. **文件格式**：链接文件必须是UTF-8编码的文本文件
4. **权限问题**：确保程序有写入文件的权限（保存cookies）
5. **防火墙**：如果有防火墙，请允许Chrome和Python的网络访问

## 🐛 常见问题

### Q: 程序提示"未找到存到我的网盘按钮"
A: 这可能是由于百度网盘页面更新导致的。程序已包含多种选择器，如果仍然失败，请检查网络连接或稍后重试。

### Q: cookies自动登录失败
A: cookies可能已过期，程序会自动打开登录页面，请重新登录即可。

### Q: 文件夹创建失败
A: 请确保百度网盘中存在"hh客户"文件夹，或者程序会尝试在根目录创建。

### Q: 程序运行缓慢
A: 这是正常现象，因为需要等待页面加载。可以通过日志查看详细进度。

## 📈 版本更新

### v2.0 (当前版本)
- ✅ 新增自动保存/加载登录状态功能
- ✅ 优化用户界面，添加进度显示
- ✅ 改进错误处理和重试机制
- ✅ 增强页面元素识别能力
- ✅ 添加详细的日志和统计功能

### v1.0 (原始版本)
- ✅ 基础的批量转存功能
- ✅ 简单的图形界面
- ✅ 基本的错误处理

## 🤝 技术支持

如果遇到问题，请：
1. 查看程序日志输出
2. 确认网络连接正常
3. 检查百度网盘是否正常访问
4. 尝试重新登录

---

**享受更便捷的百度网盘批量转存体验！** 🎉

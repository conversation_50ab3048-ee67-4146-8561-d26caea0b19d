import sys
import threading
import time
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QFileDialog, QPlainTextEdit, QLabel, QLineEdit, QProgressBar,
    QGroupBox, QGridLayout, QMessageBox, QCheckBox
)
from PyQt6.QtCore import pyqtSignal, QObject, QTimer
from PyQt6.QtGui import QFont, QIcon
from main_optimized import BaiduPanSaver

class Communicate(QObject):
    """用于从工作线程向GUI线程发送信号。"""
    log_signal = pyqtSignal(str)
    task_finished_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, int)  # current, total

class BaiduPanGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.saver = None
        self.comm = Communicate()
        self.is_processing = False
        self.init_ui()

    def init_ui(self):
        """初始化UI界面。"""
        self.setWindowTitle('百度网盘批量转存工具 v2.0')
        self.setGeometry(300, 300, 700, 600)
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 10pt;
                background-color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #c0c0c0;
                color: #333333;
                padding: 8px 16px;
                text-align: center;
                font-size: 10pt;
                border-radius: 3px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #a0a0a0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
                border-color: #e0e0e0;
            }
            QPushButton#login_button {
                background-color: #e3f2fd;
                border-color: #90caf9;
                color: #1976d2;
            }
            QPushButton#login_button:hover {
                background-color: #bbdefb;
            }
            QPushButton#save_button {
                background-color: #fff3e0;
                border-color: #ffcc02;
                color: #f57c00;
            }
            QPushButton#save_button:hover {
                background-color: #ffe0b2;
            }
            QPushButton#exit_button {
                background-color: #ffebee;
                border-color: #ef9a9a;
                color: #d32f2f;
            }
            QPushButton#exit_button:hover {
                background-color: #ffcdd2;
            }
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                padding: 5px;
                font-size: 10pt;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #90caf9;
            }
            QPlainTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                background-color: #fafafa;
                color: #333333;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 9pt;
                line-height: 1.4;
            }
            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                text-align: center;
                background-color: #f5f5f5;
            }
            QProgressBar::chunk {
                background-color: #90caf9;
                border-radius: 2px;
            }
            QCheckBox {
                color: #333333;
            }
            QLabel {
                color: #333333;
            }
        """)

        # 整体垂直布局
        main_layout = QVBoxLayout()

        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QGridLayout()

        file_layout.addWidget(QLabel('链接文件:'), 0, 0)
        self.file_path_edit = QLineEdit('links.txt')
        file_layout.addWidget(self.file_path_edit, 0, 1)

        self.browse_button = QPushButton('浏览')
        self.browse_button.clicked.connect(self.browse_file)
        file_layout.addWidget(self.browse_button, 0, 2)

        # 添加自动保存cookies选项
        self.auto_save_cookies = QCheckBox('自动保存登录状态（推荐）')
        self.auto_save_cookies.setChecked(True)
        file_layout.addWidget(self.auto_save_cookies, 1, 0, 1, 3)

        file_group.setLayout(file_layout)
        main_layout.addWidget(file_group)

        # 操作控制组
        control_group = QGroupBox("操作控制")
        control_layout = QHBoxLayout()

        self.login_button = QPushButton('1. 登录百度账号')
        self.login_button.setObjectName("login_button")
        self.login_button.clicked.connect(self.login)

        self.save_button = QPushButton('2. 开始转存')
        self.save_button.setObjectName("save_button")
        self.save_button.clicked.connect(self.start_saving)
        self.save_button.setEnabled(False)

        control_layout.addWidget(self.login_button)
        control_layout.addWidget(self.save_button)
        control_group.setLayout(control_layout)
        main_layout.addWidget(control_group)

        # 进度显示组
        progress_group = QGroupBox("进度显示")
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel('就绪')
        progress_layout.addWidget(self.status_label)

        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)

        # 日志输出组
        log_group = QGroupBox("日志输出")
        log_layout = QVBoxLayout()

        self.log_display = QPlainTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMaximumBlockCount(1000)  # 限制日志行数
        log_layout.addWidget(self.log_display)

        # 日志控制按钮
        log_control_layout = QHBoxLayout()
        self.clear_log_button = QPushButton('清空日志')
        self.clear_log_button.clicked.connect(self.clear_log)
        self.save_log_button = QPushButton('保存日志')
        self.save_log_button.clicked.connect(self.save_log)

        log_control_layout.addWidget(self.clear_log_button)
        log_control_layout.addWidget(self.save_log_button)
        log_control_layout.addStretch()

        log_layout.addLayout(log_control_layout)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 退出按钮
        self.exit_button = QPushButton('退出程序')
        self.exit_button.setObjectName("exit_button")
        self.exit_button.clicked.connect(self.close)
        main_layout.addWidget(self.exit_button)

        self.setLayout(main_layout)

        # 连接信号
        self.comm.log_signal.connect(self.append_log)
        self.comm.task_finished_signal.connect(self.on_task_finished)
        self.comm.progress_signal.connect(self.update_progress)
        
        # 添加欢迎信息
        self.append_log("欢迎使用百度网盘批量转存工具 v2.0")
        self.append_log("新功能：自动保存登录状态，无需重复登录")
        self.append_log("请先选择包含链接的文件，然后点击登录按钮开始使用")

    def append_log(self, message):
        """将日志追加到文本框中。"""
        self.log_display.appendPlainText(message)
        # 自动滚动到底部
        scrollbar = self.log_display.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_log(self):
        """清空日志。"""
        self.log_display.clear()
        self.append_log("日志已清空")

    def save_log(self):
        """保存日志到文件。"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"baidu_pan_log_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_display.toPlainText())
            self.append_log(f"日志已保存到: {filename}")
        except Exception as e:
            self.append_log(f"保存日志失败: {e}")

    def browse_file(self):
        """打开文件对话框选择文件。"""
        file_name, _ = QFileDialog.getOpenFileName(
            self, '选择链接文件', '', 'Text Files (*.txt);;All Files (*)'
        )
        if file_name:
            self.file_path_edit.setText(file_name)
            self.append_log(f"已选择文件: {file_name}")

    def update_progress(self, current, total):
        """更新进度条。"""
        if total > 0:
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(total)
            self.progress_bar.setValue(current)
            self.status_label.setText(f"进度: {current}/{total} ({current/total*100:.1f}%)")
        else:
            self.progress_bar.setVisible(False)
            self.status_label.setText("就绪")

    def login(self):
        """处理登录逻辑。"""
        if not self.saver:
            self.saver = BaiduPanSaver(log_callback=self.comm.log_signal.emit)

        self.append_log('准备登录百度网盘...')
        self.login_button.setEnabled(False)
        self.login_button.setText('登录中...')

        # 在后台线程中处理登录
        threading.Thread(target=self.login_thread_func, daemon=True).start()

    def login_thread_func(self):
        """后台线程执行登录。"""
        try:
            if self.saver.open_login_page():
                # 自动登录成功
                self.comm.log_signal.emit('自动登录成功')
                self.comm.task_finished_signal.emit('login_success')
            else:
                # 需要手动登录
                self.comm.log_signal.emit('请在浏览器中完成登录...')
                if self.saver.wait_for_login():
                    self.comm.task_finished_signal.emit('login_success')
                else:
                    self.comm.task_finished_signal.emit('login_failed')
        except Exception as e:
            self.comm.log_signal.emit(f'登录过程出错: {e}')
            self.comm.task_finished_signal.emit('login_failed')

    def start_saving(self):
        """启动后台线程进行转存。"""
        links_file = self.file_path_edit.text().strip()
        if not links_file:
            QMessageBox.warning(self, '警告', '请先选择链接文件！')
            return

        if self.is_processing:
            QMessageBox.information(self, '提示', '任务正在进行中，请稍候...')
            return

        self.is_processing = True
        self.save_button.setEnabled(False)
        self.save_button.setText('转存中...')
        self.login_button.setEnabled(False)

        self.append_log('开始转存任务...')
        
        # 将转存操作放入后台线程
        thread = threading.Thread(target=self.save_thread_func, args=(links_file,), daemon=True)
        thread.start()

    def save_thread_func(self, links_file):
        """后台线程执行的函数。"""
        try:
            if self.saver:
                result = self.saver.save_links(links_file)
                if result:
                    self.comm.task_finished_signal.emit('save_success')
                else:
                    self.comm.task_finished_signal.emit('save_failed')
            else:
                self.comm.task_finished_signal.emit('save_error')
        except Exception as e:
            self.comm.log_signal.emit(f'转存过程出错: {e}')
            self.comm.task_finished_signal.emit('save_error')

    def on_task_finished(self, task_type):
        """任务完成后的回调。"""
        if task_type == 'login_success':
            self.login_button.setText('登录成功')
            self.save_button.setEnabled(True)
            self.status_label.setText('已登录，可以开始转存')
        elif task_type == 'login_failed':
            self.login_button.setText('登录失败')
            self.login_button.setEnabled(True)
            self.status_label.setText('登录失败，请重试')
        elif task_type in ['save_success', 'save_failed', 'save_error']:
            self.is_processing = False
            self.save_button.setText('2. 开始转存')
            self.save_button.setEnabled(True)
            self.login_button.setEnabled(True)
            self.progress_bar.setVisible(False)

            if task_type == 'save_success':
                self.status_label.setText('转存任务完成')
                QMessageBox.information(self, '成功', '转存任务已完成！')
            else:
                self.status_label.setText('转存任务失败')
                QMessageBox.warning(self, '失败', '转存任务失败，请查看日志了解详情。')

    def closeEvent(self, event):
        """重写关闭事件，确保WebDriver被关闭。"""
        if self.is_processing:
            reply = QMessageBox.question(
                self, '确认退出', 
                '任务正在进行中，确定要退出吗？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
        
        if self.saver:
            self.saver.close()
        event.accept()

def main():
    app = QApplication(sys.argv)
    app.setApplicationName("百度网盘批量转存工具")
    app.setApplicationVersion("2.0")
    
    gui = BaiduPanGUI()
    gui.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()

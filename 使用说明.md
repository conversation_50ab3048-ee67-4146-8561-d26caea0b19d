# 百度网盘批量转存工具 - 使用说明

## 🔧 最新修复 (v2.2)

### 主要问题修复
1. **浏览器窗口关闭问题**：
   - 自动检测浏览器连接状态
   - 浏览器意外关闭时自动恢复
   - 保持登录状态，无需重新登录

2. **进度保存功能**：
   - 自动保存处理进度
   - 程序中断后可从上次位置继续
   - 避免重复处理已完成的链接

3. **稳定性增强**：
   - 添加更多Chrome稳定性选项
   - 改进页面加载等待机制
   - 增强错误处理和重试逻辑

## 🚀 快速开始

### 1. 启动程序
```bash
# 启动图形界面（推荐）
python3 gui_optimized.py

# 或命令行版本
python3 main_optimized.py
```

### 2. 准备链接文件
创建一个文本文件，每行一个链接：
```
https://pan.baidu.com/s/1xxxxxxxxx?pwd=xxxx
https://pan.baidu.com/s/1yyyyyyyyy?pwd=yyyy
https://pan.baidu.com/s/1zzzzzzzzz
```

### 3. 操作步骤
1. **选择文件**：点击"浏览"选择链接文件
2. **登录账号**：点击"登录百度账号"
   - 首次使用：手动登录，程序会自动保存登录状态
   - 再次使用：自动登录，无需重复操作
3. **开始转存**：点击"开始转存"
4. **查看进度**：在日志区域查看实时进度

## 🛠️ 新功能特性

### 自动恢复机制
- **浏览器检测**：实时监控浏览器连接状态
- **自动恢复**：浏览器关闭时自动重新启动并登录
- **进度保持**：恢复后从中断位置继续处理

### 进度保存
- **断点续传**：程序意外关闭后可继续处理
- **进度文件**：自动保存在 `progress.json`
- **智能跳过**：已处理的链接自动跳过

### 稳定性优化
- **Chrome选项**：添加多项稳定性参数
- **超时设置**：合理的页面加载超时时间
- **错误重试**：智能重试失败的操作

## 🎨 界面特色

### 灰黑色主题
- **护眼设计**：深色背景减少眼部疲劳
- **高对比度**：确保文字清晰可读
- **专业外观**：简洁现代的设计风格

### 日志显示
- **实时更新**：显示详细的处理过程
- **时间戳**：每条日志都有精确时间
- **颜色区分**：不同级别的日志用不同颜色显示

## 📁 文件说明

- `main_optimized.py` - 优化后的核心程序
- `gui_optimized.py` - 图形界面程序
- `baidu_cookies.json` - 自动保存的登录状态
- `progress.json` - 自动保存的处理进度
- `links.txt` - 链接文件（用户创建）

## ⚠️ 注意事项

### 链接格式
- 支持URL中包含提取码：`?pwd=xxxx`
- 支持传统格式：链接和提取码分开
- 每行一个链接，空行和#开头的行会被忽略

### 文件夹命名
- 程序根据链接文件名的后4位字符创建文件夹
- 例如：`links_1234.txt` → 创建文件夹 `1234`
- 文件保存路径：`/hh客户/[文件夹名]/`

### 浏览器要求
- 需要安装Chrome浏览器
- 程序会自动下载匹配的ChromeDriver
- 建议使用最新版本的Chrome

## 🐛 故障排除

### 常见问题

1. **浏览器窗口关闭**
   - 程序会自动检测并恢复
   - 无需手动重启，等待自动恢复即可

2. **保存按钮找不到**
   - 程序会显示详细的调试信息
   - 自动尝试多种查找方法
   - 使用JavaScript智能查找

3. **进度丢失**
   - 程序自动保存进度到 `progress.json`
   - 重启后会自动从上次位置继续

4. **登录状态丢失**
   - 程序自动保存cookies到 `baidu_cookies.json`
   - 下次启动时自动加载登录状态

### 调试信息
程序会显示详细的调试信息：
- 页面元素查找结果
- 浏览器连接状态
- 错误详细信息
- 处理进度统计

## 📞 技术支持

如果遇到问题：
1. 查看程序日志输出的详细信息
2. 检查网络连接是否正常
3. 确认Chrome浏览器版本是否最新
4. 查看是否有防火墙阻止程序运行

---

**享受更稳定、更智能的批量转存体验！** 🎉

import time
import os
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class BaiduPanSaver:
    def __init__(self, log_callback=None):
        self.driver = None
        self.log_callback = log_callback
        self.cookies_file = "baidu_cookies.json"
        self.is_logged_in = False

    def _log(self, message, level='info'):
        """统一的日志记录方法，支持回调到GUI。"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [{level.upper()}] {message}"
        
        if self.log_callback:
            # 回调给GUI
            self.log_callback(formatted_message)
        else:
            # 命令行模式下，使用标准logging
            if level == 'info':
                logging.info(message)
            elif level == 'warning':
                logging.warning(message)
            else:
                logging.error(message)

    def _initialize_driver(self):
        """初始化 Chrome WebDriver。"""
        self._log("正在初始化 Chrome WebDriver...")
        options = webdriver.ChromeOptions()
        # 添加一些有用的选项
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        # 如果需要，可以在这里添加选项，例如无头模式
        # options.add_argument('--headless')
        
        self.driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options)
        # 执行脚本来隐藏webdriver属性
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self._log("WebDriver 初始化完成。")

    def save_cookies(self):
        """保存当前的cookies到文件。"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            self._log(f"✅ Cookies已保存到 {self.cookies_file}")
        except Exception as e:
            self._log(f"❌ 保存cookies失败: {e}", level='error')

    def load_cookies(self):
        """从文件加载cookies。"""
        try:
            if os.path.exists(self.cookies_file):
                with open(self.cookies_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                
                # 先访问百度网盘主页
                self.driver.get("https://pan.baidu.com")
                time.sleep(2)
                
                # 添加cookies
                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except Exception as e:
                        self._log(f"添加cookie失败: {e}", level='warning')
                
                # 刷新页面使cookies生效
                self.driver.refresh()
                time.sleep(3)
                
                # 检查是否登录成功
                if self.check_login_status():
                    self._log("✅ 使用保存的cookies登录成功！")
                    self.is_logged_in = True
                    return True
                else:
                    self._log("⚠️ cookies已过期，需要重新登录", level='warning')
                    return False
            else:
                self._log("📝 未找到保存的cookies文件")
                return False
        except Exception as e:
            self._log(f"❌ 加载cookies失败: {e}", level='error')
            return False

    def check_login_status(self):
        """检查是否已登录。"""
        try:
            # 检查页面是否包含用户信息
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # 查找登录状态的标识元素
            login_indicators = [
                "//div[contains(@class, 'user-info')]",
                "//div[contains(@class, 'avatar')]",
                "//span[contains(@class, 'username')]",
                "//*[contains(@class, 'user-name')]",
                "//div[contains(@class, 'user-avatar')]"
            ]
            
            for indicator in login_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element and element.is_displayed():
                        return True
                except:
                    continue
            
            return False
        except Exception as e:
            self._log(f"检查登录状态失败: {e}", level='error')
            return False

    def open_login_page(self):
        """打开百度网盘页面让用户登录，此方法不阻塞。"""
        if not self.driver:
            self._initialize_driver()
        
        # 首先尝试使用保存的cookies登录
        self._log("🔄 尝试使用保存的cookies自动登录...")
        if self.load_cookies():
            self._log("🎉 自动登录成功！无需手动登录。")
            return True
        
        # 如果cookies登录失败，则打开登录页面
        self._log("🌐 自动登录失败，打开登录页面...")
        self.driver.get("https://pan.baidu.com")
        self._log("👤 请在浏览器窗口中登录您的百度账号。登录后，程序将自动保存登录状态。")
        return False

    def wait_for_login(self, timeout=300):
        """等待用户完成登录，并保存cookies。"""
        self._log(f"⏳ 等待用户登录中... (超时时间: {timeout}秒)")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.check_login_status():
                self._log("🎉 检测到登录成功！")
                self.save_cookies()
                self.is_logged_in = True
                return True
            time.sleep(2)
        
        self._log("⏰ 等待登录超时", level='error')
        return False

    def create_target_folder(self, target_folder_name):
        """创建目标文件夹的优化方法"""
        try:
            self._log(f"📁 开始创建目标文件夹: {target_folder_name}")
            
            # 点击"全部文件"以确保从根目录开始
            root_selectors = [
                "//li[contains(@class, 'treeview-node') and @title='全部文件']",
                "//span[text()='全部文件']/..",
                "//*[contains(text(), '全部文件')]"
            ]
            
            for selector in root_selectors:
                try:
                    root_element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    root_element.click()
                    self._log("✅ 已选择根目录")
                    time.sleep(1)
                    break
                except:
                    continue
            
            # 查找并点击"hh客户"文件夹
            hh_selectors = [
                "//li[contains(@class, 'treeview-node') and @title='hh客户']",
                "//span[text()='hh客户']/..",
                "//*[contains(text(), 'hh客户')]"
            ]
            
            hh_found = False
            for selector in hh_selectors:
                try:
                    hh_element = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    hh_element.click()
                    self._log("✅ 已进入 'hh客户' 文件夹")
                    time.sleep(1)
                    hh_found = True
                    break
                except:
                    continue
            
            if not hh_found:
                self._log("⚠️ 未找到'hh客户'文件夹，将在根目录创建", level='warning')
            
            # 点击"新建文件夹"按钮
            new_folder_selectors = [
                "a.g-button[title='新建文件夹']",
                "//a[contains(@title, '新建文件夹')]",
                "//button[contains(text(), '新建文件夹')]",
                "//*[contains(@class, 'new-folder')]"
            ]
            
            for selector in new_folder_selectors:
                try:
                    if selector.startswith("//"):
                        new_folder_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        new_folder_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    new_folder_btn.click()
                    self._log("✅ 已点击新建文件夹按钮")
                    time.sleep(1)
                    break
                except:
                    continue
            
            # 输入新文件夹的名称
            input_selectors = [
                "div.new-dir-item input.dir-name-input",
                "input[placeholder*='文件夹']",
                "//input[contains(@class, 'dir-name')]",
                "//input[contains(@placeholder, '文件夹')]"
            ]
            
            for selector in input_selectors:
                try:
                    if selector.startswith("//"):
                        folder_input = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                    else:
                        folder_input = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                    folder_input.clear()
                    folder_input.send_keys(target_folder_name)
                    self._log(f"✅ 已输入文件夹名称: {target_folder_name}")
                    break
                except:
                    continue
            
            # 确认创建文件夹
            confirm_selectors = [
                "a.g-button.g-button-blue-large[title='确定']",
                "//a[contains(@class, 'g-button') and contains(text(), '确定')]",
                "//button[contains(text(), '确定')]",
                "//*[@title='确定']"
            ]
            
            for selector in confirm_selectors:
                try:
                    if selector.startswith("//"):
                        confirm_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                    else:
                        confirm_btn = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                    confirm_btn.click()
                    self._log(f"🎉 文件夹 '{target_folder_name}' 创建成功！")
                    time.sleep(2)
                    return True
                except:
                    continue
            
            self._log("❌ 创建文件夹失败", level='error')
            return False

        except Exception as e:
            self._log(f"❌ 创建文件夹时发生错误: {e}", level='error')
            return False

    def save_links(self, links_file):
        """从指定的文件中读取链接，创建特定文件夹，并逐个保存。"""
        if not self.driver:
            self._log("❌ 驱动未初始化。请先登录。", level='error')
            return False

        if not self.is_logged_in:
            self._log("❌ 请先完成登录。", level='error')
            return False

        # 1. 根据links.txt的文件名计算目标文件夹名
        base_name = os.path.basename(links_file)
        file_name_without_ext = os.path.splitext(base_name)[0]
        if len(file_name_without_ext) < 4:
            self._log(f"📝 文件名 '{file_name_without_ext}' 少于4个字符，将直接使用完整文件名作为文件夹名。")
            target_folder_name = file_name_without_ext
        else:
            target_folder_name = file_name_without_ext[-4:]

        self._log(f"📁 目标文件夹: /hh客户/{target_folder_name}")

        # 2. 将所有链接读入列表
        links_to_save = []
        try:
            with open(links_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    parts = line.split()
                    if not parts:
                        continue
                    url = parts[0]
                    code = parts[1] if len(parts) > 1 else None
                    links_to_save.append({'url': url, 'code': code, 'line': line_num})
        except FileNotFoundError:
            self._log(f"❌ 文件 {links_file} 不存在", level='error')
            return False
        except Exception as e:
            self._log(f"❌ 读取文件失败: {e}", level='error')
            return False

        if not links_to_save:
            self._log("⚠️ 链接文件为空或格式不正确，任务结束。", level='warning')
            return False

        self._log(f"📋 共找到 {len(links_to_save)} 个链接待处理")

        # 3. 循环处理每个链接
        is_first_link = True
        success_count = 0
        failed_count = 0

        for i, link_info in enumerate(links_to_save, 1):
            url = link_info['url']
            code = link_info['code']
            line_num = link_info['line']

            self._log(f"🔄 [{i}/{len(links_to_save)}] 处理第{line_num}行链接: {url}")

            try:
                # 访问链接
                self.driver.get(url)
                time.sleep(3)

                # 如果有提取码，输入提取码
                if code:
                    self._log(f"🔑 输入提取码: {code}")
                    try:
                        code_input = WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.ID, "accessCode"))
                        )
                        code_input.clear()
                        code_input.send_keys(code)

                        submit_btn = self.driver.find_element(By.ID, "submitBtn")
                        submit_btn.click()
                        time.sleep(3)
                    except Exception as e:
                        self._log(f"⚠️ 输入提取码失败: {e}", level='warning')

                # 等待文件列表加载完成
                self._log("⏳ 等待文件列表加载...")
                WebDriverWait(self.driver, 20).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.file-list")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.share-file-viewer-main")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".file-item")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".list-view"))
                    )
                )
                self._log("✅ 文件列表加载成功")

                # 点击"存到我的网盘"按钮
                self._log("🔍 查找'存到我的网盘'按钮...")
                save_button_selectors = [
                    "//a[contains(@title, '存到我的网盘')]",
                    "//button[contains(@title, '存到我的网盘')]",
                    "//span[contains(text(), '存到我的网盘')]/..",
                    "//a[contains(text(), '存到我的网盘')]",
                    "//*[@data-button-id='b200']"
                ]

                save_button = None
                for selector in save_button_selectors:
                    try:
                        save_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        break
                    except:
                        continue

                if not save_button:
                    self._log("❌ 未找到'存到我的网盘'按钮", level='error')
                    failed_count += 1
                    continue

                save_button.click()
                self._log("✅ 已点击'存到我的网盘'按钮")
                time.sleep(3)

                # 等待保存对话框出现
                self._log("⏳ 等待保存对话框...")
                dialog_found = False
                dialog_selectors = [
                    "//h3[text()='存到我的网盘']",
                    "//div[contains(@class, 'save-dialog')]",
                    "//*[contains(text(), '选择存储位置')]"
                ]

                for selector in dialog_selectors:
                    try:
                        WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        dialog_found = True
                        break
                    except:
                        continue

                if not dialog_found:
                    self._log("❌ 保存对话框未出现", level='error')
                    failed_count += 1
                    continue

                self._log("✅ 保存对话框已打开")

                # 如果是第一个链接，则执行创建文件夹的逻辑
                if is_first_link:
                    if self.create_target_folder(target_folder_name):
                        is_first_link = False
                    else:
                        self._log("❌ 创建文件夹失败，跳过此链接", level='error')
                        failed_count += 1
                        continue

                # 点击最终的"确定"按钮来保存文件
                self._log("🔍 查找确定按钮...")
                confirm_selectors = [
                    "//div[@class='dialog-footer']//a[.//span[text()='确定']]",
                    "//a[contains(@class, 'g-button') and contains(text(), '确定')]",
                    "//button[contains(text(), '确定')]",
                    "//span[text()='确定']/..",
                    "//*[@title='确定']"
                ]

                confirm_button = None
                for selector in confirm_selectors:
                    try:
                        confirm_button = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        break
                    except:
                        continue

                if not confirm_button:
                    self._log("❌ 未找到确定按钮", level='error')
                    failed_count += 1
                    continue

                confirm_button.click()
                self._log("✅ 已点击确定按钮")

                # 等待并确认"存入成功"的提示
                self._log("⏳ 等待保存结果...")
                try:
                    success_msg_selectors = [
                        "//*[contains(text(), '存入成功')]",
                        "//*[contains(text(), '已存入')]",
                        "//*[contains(text(), '保存成功')]",
                        "//*[contains(text(), '转存成功')]"
                    ]

                    success_detected = False
                    for selector in success_msg_selectors:
                        try:
                            WebDriverWait(self.driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, selector))
                            )
                            success_detected = True
                            break
                        except:
                            continue

                    if success_detected:
                        self._log(f"🎉 链接 {url} 保存成功！")
                        success_count += 1
                        # 等待成功提示消失
                        time.sleep(2)
                    else:
                        self._log(f"⚠️ 链接 {url} 的保存请求已发送，但未检测到成功提示", level='warning')
                        success_count += 1  # 仍然计为成功，因为可能只是提示检测失败

                except Exception as e:
                    self._log(f"⚠️ 检测保存结果时出错: {e}", level='warning')
                    success_count += 1  # 仍然计为成功

            except Exception as e:
                self._log(f"❌ 保存链接 {url} 失败。错误: {e}", level='error')
                failed_count += 1
                continue # 继续处理下一个链接

        # 输出最终统计结果
        self._log(f"📊 任务完成！成功: {success_count}, 失败: {failed_count}, 总计: {len(links_to_save)}")
        return success_count > 0

    def close(self):
        """关闭 WebDriver。"""
        if self.driver:
            self.driver.quit()
            self._log("🔒 WebDriver 已关闭。")

def main():
    """命令行版本的执行函数。"""
    links_file = "links.txt"
    saver = BaiduPanSaver()

    # 尝试自动登录
    if not saver.open_login_page():
        # 如果自动登录失败，等待用户手动登录
        if not saver.wait_for_login():
            print("登录失败，程序退出。")
            saver.close()
            return

    # 开始保存链接
    saver.save_links(links_file)
    saver.close()
    logging.info("所有链接处理完毕。")

if __name__ == "__main__":
    main()

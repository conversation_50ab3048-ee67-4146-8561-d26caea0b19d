import time
import os
import json
import pickle
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class BaiduPanSaver:
    def __init__(self, log_callback=None):
        self.driver = None
        self.log_callback = log_callback
        self.cookies_file = "baidu_cookies.json"
        self.is_logged_in = False

    def _log(self, message, level='info'):
        """统一的日志记录方法，支持回调到GUI。"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [{level.upper()}] {message}"

        if self.log_callback:
            # 回调给GUI
            self.log_callback(formatted_message)
        else:
            # 命令行模式下，使用标准logging
            if level == 'info':
                logging.info(message)
            elif level == 'warning':
                logging.warning(message)
            else:
                logging.error(message)

    def _initialize_driver(self):
        """初始化 Chrome WebDriver。"""
        self._log("正在初始化 Chrome WebDriver...")
        options = webdriver.ChromeOptions()
        # 添加一些有用的选项
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        # 如果需要，可以在这里添加选项，例如无头模式
        # options.add_argument('--headless')

        self.driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options)
        # 执行脚本来隐藏webdriver属性
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self._log("WebDriver 初始化完成。")

    def save_cookies(self):
        """保存当前的cookies到文件。"""
        try:
            cookies = self.driver.get_cookies()
            with open(self.cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            self._log(f"Cookies已保存到 {self.cookies_file}")
        except Exception as e:
            self._log(f"保存cookies失败: {e}", level='error')

    def load_cookies(self):
        """从文件加载cookies。"""
        try:
            if os.path.exists(self.cookies_file):
                with open(self.cookies_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)

                # 先访问百度网盘主页
                self.driver.get("https://pan.baidu.com")
                time.sleep(2)

                # 添加cookies
                for cookie in cookies:
                    try:
                        self.driver.add_cookie(cookie)
                    except Exception as e:
                        self._log(f"添加cookie失败: {e}", level='warning')

                # 刷新页面使cookies生效
                self.driver.refresh()
                time.sleep(3)

                # 检查是否登录成功
                if self.check_login_status():
                    self._log("使用保存的cookies登录成功！")
                    self.is_logged_in = True
                    return True
                else:
                    self._log("cookies已过期，需要重新登录", level='warning')
                    return False
            else:
                self._log("未找到保存的cookies文件")
                return False
        except Exception as e:
            self._log(f"加载cookies失败: {e}", level='error')
            return False

    def check_login_status(self):
        """检查是否已登录。"""
        try:
            # 检查页面是否包含用户信息
            WebDriverWait(self.driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            # 查找登录状态的标识元素
            login_indicators = [
                "//div[contains(@class, 'user-info')]",
                "//div[contains(@class, 'avatar')]",
                "//span[contains(@class, 'username')]",
                "//*[contains(@class, 'user-name')]"
            ]

            for indicator in login_indicators:
                try:
                    element = self.driver.find_element(By.XPATH, indicator)
                    if element and element.is_displayed():
                        return True
                except:
                    continue

            return False
        except Exception as e:
            self._log(f"检查登录状态失败: {e}", level='error')
            return False

    def open_login_page(self):
        """打开百度网盘页面让用户登录，此方法不阻塞。"""
        if not self.driver:
            self._initialize_driver()

        # 首先尝试使用保存的cookies登录
        self._log("尝试使用保存的cookies自动登录...")
        if self.load_cookies():
            self._log("✅ 自动登录成功！无需手动登录。")
            return True

        # 如果cookies登录失败，则打开登录页面
        self._log("自动登录失败，打开登录页面...")
        self.driver.get("https://pan.baidu.com")
        self._log("请在浏览器窗口中登录您的百度账号。登录后，程序将自动保存登录状态。")
        return False

    def wait_for_login(self, timeout=300):
        """等待用户完成登录，并保存cookies。"""
        self._log(f"等待用户登录中... (超时时间: {timeout}秒)")
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.check_login_status():
                self._log("✅ 检测到登录成功！")
                self.save_cookies()
                self.is_logged_in = True
                return True
            time.sleep(2)

        self._log("等待登录超时", level='error')
        return False

    def save_links(self, links_file):
        """从指定的文件中读取链接，创建特定文件夹，并逐个保存。"""
        if not self.driver:
            self._log("驱动未初始化。请先登录。", level='error')
            return False

        if not self.is_logged_in:
            self._log("请先完成登录。", level='error')
            return False

        # 1. 根据links.txt的文件名计算目标文件夹名
        base_name = os.path.basename(links_file)
        file_name_without_ext = os.path.splitext(base_name)[0]
        if len(file_name_without_ext) < 4:
            self._log(f"文件名 '{file_name_without_ext}' 少于4个字符，将直接使用完整文件名作为文件夹名。")
            target_folder_name = file_name_without_ext
        else:
            target_folder_name = file_name_without_ext[-4:]

        self._log(f"📁 目标文件夹: /hh客户/{target_folder_name}")

        # 2. 将所有链接读入列表
        links_to_save = []
        try:
            with open(links_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    parts = line.split()
                    if not parts:
                        continue
                    url = parts[0]
                    code = parts[1] if len(parts) > 1 else None
                    links_to_save.append({'url': url, 'code': code, 'line': line_num})
        except FileNotFoundError:
            self._log(f"文件 {links_file} 不存在", level='error')
            return False
        except Exception as e:
            self._log(f"读取文件失败: {e}", level='error')
            return False

        if not links_to_save:
            self._log("❌ 链接文件为空或格式不正确，任务结束。", level='warning')
            return False

        self._log(f"📋 共找到 {len(links_to_save)} 个链接待处理")

        # 3. 循环处理每个链接
        is_first_link = True
        success_count = 0
        failed_count = 0

        for i, link_info in enumerate(links_to_save, 1):
            url = link_info['url']
            code = link_info['code']
            line_num = link_info['line']

            self._log(f"🔄 [{i}/{len(links_to_save)}] 处理第{line_num}行链接: {url}")

            try:
                # 访问链接
                self.driver.get(url)
                time.sleep(2)

                # 如果有提取码，输入提取码
                if code:
                    self._log(f"🔑 输入提取码: {code}")
                    try:
                        code_input = WebDriverWait(self.driver, 10).until(
                            EC.presence_of_element_located((By.ID, "accessCode"))
                        )
                        code_input.clear()
                        code_input.send_keys(code)

                        submit_btn = self.driver.find_element(By.ID, "submitBtn")
                        submit_btn.click()
                        time.sleep(3)
                    except Exception as e:
                        self._log(f"输入提取码失败: {e}", level='warning')

                # 等待文件列表加载完成
                self._log("⏳ 等待文件列表加载...")
                WebDriverWait(self.driver, 20).until(
                    EC.any_of(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.file-list")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.share-file-viewer-main")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".file-item")),
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".list-view"))
                    )
                )
                self._log("✅ 文件列表加载成功")

                # 点击"存到我的网盘"按钮
                save_button_locator = (By.XPATH, "//a[contains(@title, '存到我的网盘')] | //button[contains(@title, '存到我的网盘')]")
                WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(save_button_locator)).click()
                
                # 等待保存对话框出现
                dialog_title_locator = (By.XPATH, "//h3[text()='存到我的网盘']")
                WebDriverWait(self.driver, 10).until(EC.presence_of_element_located(dialog_title_locator))
                self._log('“存到我的网盘”对话框已加载。')
                
                # 如果是第一个链接，则执行创建文件夹的逻辑
                if is_first_link:
                    self._log("首次保存，开始创建并进入目标文件夹...")
                    
                    # 点击"全部文件"以确保从根目录开始
                    root_folder_locator = (By.XPATH, "//li[contains(@class, 'treeview-node') and @title='全部文件']")
                    WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(root_folder_locator)).click()
                    time.sleep(1)

                    # 点击"hh客户"文件夹
                    hh_folder_locator = (By.XPATH, f"//li[contains(@class, 'treeview-node') and @title='hh客户']")
                    WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(hh_folder_locator)).click()
                    self._log("已进入 'hh客户' 文件夹。")
                    time.sleep(1)

                    # 点击"新建文件夹"按钮
                    new_folder_button_locator = (By.CSS_SELECTOR, "a.g-button[title='新建文件夹']")
                    WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(new_folder_button_locator)).click()
                    
                    # 输入新文件夹的名称
                    new_folder_input_locator = (By.CSS_SELECTOR, "div.new-dir-item input.dir-name-input")
                    new_folder_input = WebDriverWait(self.driver, 10).until(EC.presence_of_element_located(new_folder_input_locator))
                    new_folder_input.send_keys(target_folder_name)
                    self._log(f"输入新文件夹名称: {target_folder_name}")

                    # 确认创建文件夹
                    confirm_new_folder_locator = (By.CSS_SELECTOR, "a.g-button.g-button-blue-large[title='确定']")
                    WebDriverWait(self.driver, 10).until(EC.element_to_be_clickable(confirm_new_folder_locator)).click()
                    self._log(f"已创建新文件夹 '{target_folder_name}'。")
                    time.sleep(2) # 等待文件夹创建并被选中
                    is_first_link = False

                # 点击最终的"确定"按钮来保存文件
                final_confirm_button_locator = (By.XPATH, "//div[@class='dialog-footer']//a[.//span[text()='确定']]")
                WebDriverWait(self.driver, 20).until(EC.element_to_be_clickable(final_confirm_button_locator)).click()
                
                # 等待并确认"存入成功"的提示
                try:
                    success_msg_locator = (By.XPATH, "//*[contains(text(), '存入成功')] | //*[contains(text(), '已存入')]")
                    WebDriverWait(self.driver, 10).until(EC.presence_of_element_located(success_msg_locator))
                    self._log(f"链接 {url} 已成功保存！")
                    WebDriverWait(self.driver, 10).until_not(EC.presence_of_element_located(success_msg_locator))
                except:
                    self._log(f"链接 {url} 的保存请求已发送，但未检测到成功提示。")
                    time.sleep(2)

            except Exception as e:
                self._log(f"保存链接 {url} 失败。错误: {e}", level='error')
                continue # 继续处理下一个链接

    def close(self):
        """关闭 WebDriver。"""
        if self.driver:
            self.driver.quit()
            self._log("WebDriver 已关闭。")

def main():
    """命令行版本的执行函数。"""
    links_file = "links.txt"
    saver = BaiduPanSaver()
    saver.open_login_page()
    input("登录完成后，请按回车键继续...")
    logging.info("登录流程完成。")
    saver.save_links(links_file)
    saver.close()
    logging.info("所有链接处理完毕。")

if __name__ == "__main__":
    main() 
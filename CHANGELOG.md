# 更新日志

## v2.1 (2024-12-22)

### 🔧 保存功能修复
- **新增更多保存按钮选择器**：支持新版百度网盘界面
  - 添加 `u-button`、`save-btn`、`btn-save` 等现代CSS类选择器
  - 支持 `aria-label` 属性查找
  - 添加工具栏和分享页面的特定选择器

- **JavaScript智能查找**：当传统选择器失败时自动启用
  - 使用JavaScript遍历页面所有元素
  - 智能匹配包含保存相关文字的按钮
  - 自动点击找到的第一个匹配按钮

- **调试信息增强**：
  - 显示找到的按钮详细信息（标签、文本、CSS类）
  - 列出页面上所有可点击元素用于调试
  - 提供详细的错误定位信息

### 🎨 界面优化
- **灰黑色主题**：
  - 主背景色：`#2b2b2b`（深灰）
  - 组件背景色：`#3a3a3a`（中灰）
  - 日志背景色：`#1e1e1e`（深黑）
  - 文字颜色：`#e0e0e0`（浅灰）

- **避免撞色**：
  - 日志区域使用深黑背景配浅灰文字
  - 按钮使用不同深度的灰色区分状态
  - 输入框使用中灰背景确保可读性

- **专业外观**：
  - 去除所有花哨颜色和表情符号
  - 使用简洁的边框和圆角设计
  - 优化字体和行高提高可读性

### 🚀 功能改进
- **链接格式支持**：
  - 支持URL中包含提取码的格式 (`?pwd=xxxx`)
  - 自动从URL中提取提取码
  - 改进提取码输入和提交逻辑

- **错误处理增强**：
  - 检测链接不存在、提取码错误等页面错误
  - 改进页面加载等待逻辑
  - 增加重试和恢复机制

- **日志系统优化**：
  - 去除表情符号，使用纯文本日志
  - 添加时间戳提高可读性
  - 优化日志级别和颜色显示

## v2.0 (2024-12-21)

### ✨ 主要新功能
- **自动登录功能**：保存和加载cookies，免重复登录
- **图形界面优化**：现代化UI设计，实时进度显示
- **批量处理改进**：更高效的文件夹创建和保存逻辑
- **详细统计信息**：显示成功/失败数量和处理时间

### 🔧 技术改进
- **多选择器支持**：每个操作都有多个备用选择器
- **智能等待机制**：使用 `EC.any_of()` 等待多种页面状态
- **错误恢复机制**：更好的异常处理和错误恢复

## v1.0 (初始版本)

### 基础功能
- 批量转存百度网盘分享链接
- 简单的图形界面
- 基本的错误处理
- 文件夹自动创建

---

## 使用说明

### 快速开始
```bash
# 启动优化后的图形界面
python3 gui_optimized.py

# 或使用测试脚本检查界面
python3 test_gui.py
```

### 链接文件格式
```
# 支持的格式
https://pan.baidu.com/s/1xxxxxxxxx xxxx
https://pan.baidu.com/s/1yyyyyyyyy?pwd=yyyy
https://pan.baidu.com/s/1zzzzzzzzz
```

### 故障排除
1. **保存按钮找不到**：程序会自动使用JavaScript查找并显示调试信息
2. **界面撞色**：已优化为灰黑色主题，避免颜色冲突
3. **登录问题**：支持自动保存和加载登录状态

---

**注意**：如果遇到问题，请查看程序日志输出获取详细的调试信息。
